import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const formItemConfig: ComponentConfig = {
  type: 'AbcFormItem',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {};

    // 基本参数检查
    if (!nodeContext || !nodeContext.node) {
      return result;
    }

    const { context, node } = nodeContext;
    const componentProperties = context?.componentProperties;

    if (componentProperties) {
      try {
        // 查找 label 节点 - 添加类型检查
        let labelNode: any = null;
        if (node && 'children' in node && Array.isArray((node as any).children)) {
          labelNode = (node as any).children?.find((child: any) =>
            child && child.name && typeof child.name === 'string' &&
            child.name.toLowerCase() === 'label'
          );
        }

        if (labelNode && labelNode.componentProperties) {
          const properties: Record<string, any> = {};

          // 处理 labelNode 的 componentProperties
          try {
            for (const [key, value] of Object.entries(labelNode.componentProperties || {})) {
              if (typeof key === 'string' && key.includes('#')) {
                const [propName] = key.split('#');
                if (propName) {
                  properties[propName] = value;
                }
              } else {
                properties[key] = value;
              }
            }

            console.log('label properties', properties);

            // 设置 label 属性
            if (properties['label'] && properties['label'].value) {
              componentProperties.label = properties['label'].value;
            }
          } catch (error) {
            console.warn('Error processing label properties:', error);
          }
        }

        // 处理 gridColumn 属性
        if (componentProperties.gridColumn) {
          const gridColumn = parseInt(componentProperties.gridColumn);
          if (!isNaN(gridColumn) && gridColumn > 0) {
            componentProperties.gridColumn = `span ${gridColumn}`;
          } else {
            componentProperties.gridColumn = 'span 1';
          }
        }

        // 处理 isExcel 属性
        if (componentProperties.isExcel !== undefined) {
            componentProperties.isExcel = componentProperties.isExcel.toLowerCase() === 'true';
        }

        console.log('formItemConfig customTransformer', componentProperties);
      } catch (error) {
        console.warn('Error in formItemConfig customTransformer:', error);
      }
    }

    return result;
  }
};
