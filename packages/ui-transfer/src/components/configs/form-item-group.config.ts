import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const formItemGroupConfig: ComponentConfig = {
  type: 'AbcFormItemGroup',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {};
    const { context } = nodeContext;
    
    const componentProperties = context?.componentProperties;
    
    console.log('formItemGroupConfig customTransformer', { context, nodeContext });
    
    if (componentProperties) {
      // 处理 gridColumnCount 属性，即分割成几列
      if (componentProperties.gridColumnCount) {
        const columnCount = parseInt(componentProperties.gridColumnCount);
        if (!isNaN(columnCount) && columnCount > 0) {
          componentProperties.gridColumnCount = columnCount;
        } else {
          // 默认值为 1
          componentProperties.gridColumnCount = 1;
        }
        componentProperties.gird = true;
      }
      
      // 处理 isExcel 属性，输入框是否有 Excel 样式
      if (componentProperties.isExcel === 'True') {
        componentProperties.isExcel = true;
      } else {
        componentProperties.isExcel = false;
      }
    }
    
    return result;
  }
};
